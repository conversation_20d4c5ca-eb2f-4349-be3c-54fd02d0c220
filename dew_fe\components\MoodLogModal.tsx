import { useState } from "react";
import { Modal, Pressable, Text, TextInput, View, Alert } from "react-native";
import Colors from "../constants/Colors";
import { supabase } from "../lib/supabase";
import { useAuth } from "../hooks/useAuth";

const MOOD_OPTIONS = [
  { emoji: "😊", label: "Happy", color: "bg-green-500", value: 90 },
  { emoji: "😌", label: "Calm", color: "bg-blue-500", value: 80 },
  { emoji: "😕", label: "Meh", color: "bg-yellow-500", value: 50 },
  { emoji: "😢", label: "Sad", color: "bg-purple-500", value: 30 },
  { emoji: "😠", label: "Angry", color: "bg-red-500", value: 20 },
  { emoji: "😰", label: "Anxious", color: "bg-orange-500", value: 25 },
];

interface MoodLogModalProps {
  visible: boolean;
  onClose: () => void;
  onSave?: (mood: { emoji: string; label: string; note: string; value: number }) => void;
}

export default function MoodLogModal({
  visible,
  onClose,
  onSave,
}: MoodLogModalProps) {
  const { userId } = useAuth();
  const [selectedMood, setSelectedMood] = useState<{
    emoji: string;
    label: string;
    value: number;
  } | null>(null);
  const [note, setNote] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!selectedMood || !userId) {
      console.log("❌ MOOD INSERT FAILED: Missing data", { selectedMood, userId });
      Alert.alert("Error", "Please select a mood and ensure you're logged in.");
      return;
    }

    // Check current Supabase session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log("🔐 CURRENT SESSION:", session);
    console.log("🔐 SESSION ERROR:", sessionError);
    console.log("🔐 USER ID FROM SESSION:", session?.user?.id);
    console.log("🔐 USER ID FROM HOOK:", userId);

    setIsLoading(true);
    try {
      const today = new Date().toISOString().split("T")[0];

      const moodData = {
        user_id: userId,
        mood_emoji: selectedMood.emoji,
        mood_label: selectedMood.label,
        mood_value: selectedMood.value,
        note: note.trim() || null,
        logged_date: today,
        created_at: new Date().toISOString(),
      };

      console.log("🚀 INSERTING MOOD LOG:", moodData);

      // Insert mood log into Supabase
      const { data, error } = await supabase
        .from("mood_logs")
        .insert(moodData)
        .select();

      if (error) {
        console.error("❌ MOOD INSERT ERROR:", error);
        Alert.alert("Error", "Failed to save your mood. Please try again.");
      } else {
        console.log("✅ MOOD INSERT SUCCESS:", data);

        // Call the optional callback for any additional handling
        onSave?.({
          emoji: selectedMood.emoji,
          label: selectedMood.label,
          note,
          value: selectedMood.value,
        });

        Alert.alert("Success", "🎉 Your mood has been logged!");
        setSelectedMood(null);
        setNote("");
        onClose();
      }
    } catch (err) {
      console.error("❌ MOOD INSERT UNEXPECTED ERROR:", err);
      Alert.alert("Error", "Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-end">
        <View
          className="rounded-t-3xl p-6"
          style={{ backgroundColor: Colors.background.elevated }}
        >
          <View className="flex-row justify-between items-center mb-6">
            <Text
              style={{ color: Colors.text.primary }}
              className="text-xl font-bold"
            >
              How are you feeling?
            </Text>
            <Pressable onPress={onClose}>
              <Text style={{ color: Colors.text.secondary }}>✕</Text>
            </Pressable>
          </View>

          <View className="flex-row flex-wrap justify-between mb-6">
            {MOOD_OPTIONS.map((mood) => (
              <Pressable
                key={mood.label}
                onPress={() => setSelectedMood({
                  emoji: mood.emoji,
                  label: mood.label,
                  value: mood.value,
                })}
                className={`w-[31%] rounded-xl p-4 mb-4 items-center ${
                  selectedMood?.label === mood.label
                    ? mood.color
                    : "bg-gray-800/30"
                }`}
              >
                <Text className="text-2xl mb-2">{mood.emoji}</Text>
                <Text
                  style={{ color: Colors.text.primary }}
                  className="text-sm"
                >
                  {mood.label}
                </Text>
              </Pressable>
            ))}
          </View>

          <View className="mb-6">
            <Text
              style={{ color: Colors.text.primary }}
              className="text-base font-medium mb-2"
            >
              Add a note (optional)
            </Text>
            <TextInput
              value={note}
              onChangeText={setNote}
              placeholder="How are you feeling today?"
              placeholderTextColor={Colors.text.secondary}
              multiline
              numberOfLines={4}
              className="rounded-xl p-4 mb-2"
              style={{
                backgroundColor: "rgba(255,255,255,0.05)",
                color: Colors.text.primary,
                borderWidth: 1,
                borderColor: "rgba(255,255,255,0.1)",
              }}
            />
          </View>

          <Pressable
            onPress={handleSave}
            disabled={!selectedMood || isLoading}
            className={`rounded-xl p-4 ${
              selectedMood && !isLoading ? "bg-purple-500" : "bg-gray-800"
            }`}
          >
            <Text
              style={{ color: Colors.text.primary }}
              className="text-center font-bold"
            >
              {isLoading ? "Saving..." : "Save Mood"}
            </Text>
          </Pressable>
        </View>
      </View>
    </Modal>
  );
}
